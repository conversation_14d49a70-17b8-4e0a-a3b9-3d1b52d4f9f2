import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:math' as math;
import 'package:xoxknit/app/core/theme/app_colors.dart';
import 'package:xoxknit/app/data/models/new_item_model.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';
import 'package:xoxknit/app/modules/shape_test/models/shape_data.dart';
import '../controllers/new_item_wizard_controller.dart';
import 'widgets/progress_tracking_shape_painter.dart';
import '../../../utils/dimension_utils.dart';

class KnittingInstructionsSummary extends GetView<NewItemWizardController> {
  const KnittingInstructionsSummary({super.key});

  @override
  Widget build(BuildContext context) {
    // Get screen size for responsive layout
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;
    final horizontalPadding = isSmallScreen ? 12.0 : 20.0;

    // Ensure statistics are available when this view is loaded
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (controller.patternStatistics.value.isEmpty) {
        try {
          if (Get.isRegistered<ShapeEditorController>()) {
            final shapeEditorController = Get.find<ShapeEditorController>();
            final stats = shapeEditorController
                .knittingInstructionsManager.patternStatistics.value;

            if (stats.isEmpty) {
              // Try to regenerate statistics
              controller.generateKnittingInstructions();
            } else {
              // Store the stats
              controller.patternStatistics.value =
                  Map<String, dynamic>.from(stats);
            }
          }
        } catch (e) {
          // Handle errors silently
        }
      }
    });

    return Scaffold(
      body: SingleChildScrollView(
        padding: EdgeInsets.all(horizontalPadding),
        child: Obx(() {
          final item = controller.newItem.value;

          // Get pattern statistics and determine if info is available
          final stats = controller.patternStatistics.value;
          final hasStats = stats.isNotEmpty &&
              stats['totalRows'] != null &&
              stats['maxWidth'] != null;

          // Check if yarn specs are available
          final hasYarnSpecs =
              item.yarnTitle != null && item.yarnTitle!.isNotEmpty;

          // Check if machine details are available
          final hasMachineDetails = item.knittingMachine != null;

          // Calculate yarn weight needed
          double yarnNeeded = 0.0;
          if (hasStats &&
              stats.containsKey('totalStitches') &&
              item.stitchesPerCm != null &&
              item.stitchesPerCm! > 0 &&
              item.rowsPerCm != null &&
              item.rowsPerCm! > 0 &&
              item.weightPer100CmSquared != null &&
              item.weightPer100CmSquared! > 0) {
            final totalStitches = stats['totalStitches'] as int? ?? 0;
            final stitchesPerCm = item.stitchesPerCm!;
            final rowsPerCm = item.rowsPerCm!;
            final weightPer100CmSquared = item.weightPer100CmSquared!;

            // Calculate total area using DimensionUtils conversions
            // We need total area = (total stitches / (stitches/cm * rows/cm))
            final stitchesPerCm2 = stitchesPerCm * rowsPerCm;
            if (totalStitches > 0 && stitchesPerCm2 > 0) {
              final patternArea = totalStitches / stitchesPerCm2;
              final weight = (patternArea / 100) * weightPer100CmSquared;
              yarnNeeded = weight * 1.1; // Add 10% waste
            }
          } else {
            // Fallback or indication that gauge info is missing
            debugPrint(
                "Cannot calculate yarn needed: Missing stats or valid gauge/weight info.");
          }

          // Get the shape data from the controller
          List<ShapeData> shapes = [];
          try {
            if (Get.isRegistered<ShapeEditorController>()) {
              final shapeEditorController = Get.find<ShapeEditorController>();
              for (final shape in shapeEditorController.shapes) {
                if (shape.key != null) {
                  final shapeData =
                      shapeEditorController.getShapeState(shape.key);
                  if (shapeData != null) {
                    shapes.add(shapeData);
                  }
                }
              }
            }
          } catch (e) {
            // Handle errors silently
          }

          // Calculate actual dimensions based on the pattern instructions and gauge using DimensionUtils
          final instructions = controller.knittingInstructions.value;
          final stitchesPerCm =
              item.stitchesPerCm ?? 1.0; // Default gauge if null
          final rowsPerCm = item.rowsPerCm ?? 1.0; // Default gauge if null

          final int topWidthStitches =
              DimensionUtils.getTopStitches(instructions);
          final int bottomWidthStitches =
              DimensionUtils.getBottomStitches(instructions);
          final int totalRows = DimensionUtils.getTotalRows(instructions);

          final double topWidthCm =
              DimensionUtils.stitchesToCm(topWidthStitches, stitchesPerCm);
          final double bottomWidthCm =
              DimensionUtils.stitchesToCm(bottomWidthStitches, stitchesPerCm);
          final double heightCm = DimensionUtils.rowsToCm(totalRows, rowsPerCm);

          // Initialize section expansion states
          final RxBool descriptionExpanded = false.obs;
          final RxBool yarnSpecsExpanded = true.obs;
          final RxBool machineDetailsExpanded = true.obs;

          // Ensure valid gauge values before passing to painter
          final validStitchesPerCm =
              math.max(0.1, stitchesPerCm); // Use calculated/default
          final validRowsPerCm =
              math.max(0.1, rowsPerCm); // Use calculated/default

          // Calculate preview size based on actual pattern dimensions
          // while respecting screen constraints
          final double maxPreviewWidth = isSmallScreen ? 300 : 400;
          final double maxPreviewHeight = isSmallScreen ? 400 : 600;

          // Calculate the actual aspect ratio of the pattern
          final double patternWidthCm = math.max(topWidthCm, bottomWidthCm);
          final double patternHeightCm = heightCm;

          // Calculate physical aspect ratio
          final double aspectRatio =
              patternWidthCm > 0 ? patternHeightCm / patternWidthCm : 1.0;

          // Calculate preview dimensions maintaining aspect ratio
          double previewWidth;
          double previewHeight;

          if (aspectRatio > maxPreviewHeight / maxPreviewWidth) {
            // Pattern is taller than the max preview aspect ratio
            // Constrain by height
            previewHeight = maxPreviewHeight;
            previewWidth = previewHeight / aspectRatio;
          } else {
            // Pattern is wider than the max preview aspect ratio
            // Constrain by width
            previewWidth = maxPreviewWidth;
            previewHeight = previewWidth * aspectRatio;
          }

          // Ensure minimum dimensions for visibility
          previewWidth = math.max(200, previewWidth);
          previewHeight = math.max(200, previewHeight);

          final Size previewSize = Size(previewWidth, previewHeight);

          // For shape preview
          final shapePainter = ProgressTrackingShapePainter(
            shapes: shapes,
            instructions: instructions, // Pass instructions
            currentRow: 0, // No current row in summary view
            totalRows: totalRows,
            progressPercentage: 0, // No progress in summary view
            topStitches: topWidthStitches, // Use calculated stitches
            bottomStitches: bottomWidthStitches, // Use calculated stitches
            topWidth:
                "${DimensionUtils.formatCmForDisplay(topWidthCm)} ${'knittingInstructions_summary_cm'.tr}", // Use standardized formatting
            bottomWidth:
                "${DimensionUtils.formatCmForDisplay(bottomWidthCm)} ${'knittingInstructions_summary_cm'.tr}", // Use standardized formatting
            height:
                "${DimensionUtils.formatCmForDisplay(heightCm)} ${'knittingInstructions_summary_cm'.tr}", // Use standardized formatting
            rowsCount: totalRows,
            leftNeedles: 0, // Not needed in summary view
            rightNeedles: 0, // Not needed in summary view
            context: context, // Pass context for theme-aware colors
            stitchesPerCm: validStitchesPerCm, // Pass valid gauge
            rowsPerCm: validRowsPerCm, // Pass valid gauge
            showProgress:
                false, // Explicitly hide progress elements in summary view
          );

          final yarnNeededDisplay =
              yarnNeeded.round(); // Use the locally calculated value

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Shape design section
              _buildSectionWithBorder(
                context: context,
                title: 'knittingInstructions_summary_shapeDesign'.tr,
                titleColor: Theme.of(context).colorScheme.primary,
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    vertical: isSmallScreen ? 20 : 30,
                    horizontal: isSmallScreen ? 10 : 20,
                  ),
                  child: Center(
                    child: CustomPaint(
                      size: Size(previewSize.width, previewSize.height),
                      painter: shapePainter,
                    ),
                  ),
                ),
              ),
              SizedBox(height: isSmallScreen ? 16 : 20),

              // General info section
              _buildSectionWithBorder(
                context: context,
                title: 'knittingInstructions_summary_generalInfo'.tr,
                titleColor: Theme.of(context).colorScheme.primary,
                child: Padding(
                  padding: EdgeInsets.all(isSmallScreen ? 12.0 : 16.0),
                  child: Column(
                    children: [
                      _buildGeneralInfoRow(
                        context: context,
                        label:
                            'knittingInstructions_summary_totalRowsToKnit'.tr,
                        value: '${stats['totalRows'] ?? 0}',
                      ),
                      const Divider(),
                      _buildGeneralInfoRow(
                        context: context,
                        label: 'knittingInstructions_summary_maximumNeedles'.tr,
                        value: '${stats['maxWidth'] ?? 0}',
                      ),
                      const Divider(),
                      _buildGeneralInfoRow(
                        context: context,
                        label:
                            'knittingInstructions_summary_totalYarnRequired'.tr,
                        value: '${yarnNeededDisplay}g',
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 12),

              // Yarn Specifications Section
              _buildExpandableSection(
                context: context,
                title: 'knittingInstructions_summary_yarnSpecifications'.tr,
                expanded: yarnSpecsExpanded,
                enabled: hasYarnSpecs,
                content: hasYarnSpecs
                    ? _buildCondensedYarnSpecs(context, item)
                    : null,
              ),
              const SizedBox(height: 12),

              // Machine Details Section
              _buildExpandableSection(
                context: context,
                title: 'knittingInstructions_summary_machineDetails'.tr,
                expanded: machineDetailsExpanded,
                enabled: hasMachineDetails,
                content: hasMachineDetails
                    ? _buildMachineDetailsContent(context, item, stats)
                    : null,
              ),
            ],
          );
        }),
      ),
    );
  }

  Widget _buildSectionWithBorder({
    required BuildContext context,
    required String title,
    required Widget child,
    Color? titleColor,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final borderColor = colorScheme.primary.withOpacity(0.3);
    final bgColor = colorScheme.surface;
    final textColor = titleColor ?? colorScheme.primary;

    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          margin: const EdgeInsets.only(top: 12),
          decoration: BoxDecoration(
            color: bgColor,
            border: Border.all(color: borderColor),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: child,
          ),
        ),
        Positioned(
          top: 0,
          left: 20,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            color: bgColor,
            child: Text(
              title,
              style: TextStyle(
                color: textColor,
                fontSize: 22,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGeneralInfoRow(
      {required BuildContext context,
      required String label,
      required String value}) {
    final primaryColor = Theme.of(context).colorScheme.primary;
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                color: primaryColor,
                fontSize: isSmallScreen ? 16 : 18,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: primaryColor,
              fontWeight: FontWeight.bold,
              fontSize: isSmallScreen ? 18 : 22,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpandableSection({
    required BuildContext context,
    required String title,
    required RxBool expanded,
    required bool enabled,
    required Widget? content,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final titleColor =
        enabled ? colorScheme.primary : colorScheme.primary.withOpacity(0.4);
    final borderColor = enabled
        ? colorScheme.primary.withOpacity(0.3)
        : colorScheme.onSurface.withOpacity(0.1);
    final bgColor = colorScheme.surface;

    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          margin: const EdgeInsets.only(top: 12),
          decoration: BoxDecoration(
            color: bgColor,
            border: Border.all(color: borderColor),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              InkWell(
                onTap: enabled ? () => expanded.toggle() : null,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      if (enabled)
                        Obx(() => Icon(
                              expanded.value
                                  ? Icons.keyboard_arrow_up
                                  : Icons.keyboard_arrow_down,
                              color: colorScheme.primary,
                            )),
                    ],
                  ),
                ),
              ),
              Obx(() => expanded.value && enabled && content != null
                  ? Padding(
                      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                      child: content,
                    )
                  : const SizedBox.shrink()),
            ],
          ),
        ),
        Positioned(
          top: 0,
          left: 20,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            color: bgColor,
            child: Text(
              title,
              style: TextStyle(
                color: titleColor,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCondensedYarnSpecs(BuildContext context, NewItemModel item) {
    return Wrap(
      spacing: 16,
      runSpacing: 16,
      children: [
        if (item.yarnTitle != null && item.yarnTitle!.isNotEmpty)
          _buildYarnSpecChip(
              context,
              'knittingInstructions_summary_yarn_title'
                  .trParams({'title': item.yarnTitle!}),
              Icons.circle),
        if (item.strands != null)
          _buildYarnSpecChip(
              context,
              'knittingInstructions_summary_yarn_strands'
                  .trParams({'count': item.strands.toString()}),
              Icons.linear_scale),
        if (item.tension != null && item.tension!.isNotEmpty)
          _buildYarnSpecChip(
              context,
              'knittingInstructions_summary_yarn_tension'
                  .trParams({'tension': item.tension!}),
              Icons.speed),
        if (item.yarnSupplier != null && item.yarnSupplier!.isNotEmpty)
          _buildYarnSpecChip(
              context,
              'knittingInstructions_summary_yarn_supplier'
                  .trParams({'supplier': item.yarnSupplier!}),
              Icons.store),
      ],
    );
  }

  Widget _buildYarnSpecChip(BuildContext context, String text, IconData icon) {
    final colorScheme = Theme.of(context).colorScheme;
    final bgColor = colorScheme.surface;
    final borderColor = colorScheme.outline;
    final textColor = colorScheme.onSurface;
    final iconColor = colorScheme.secondary;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: borderColor.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: iconColor,
          ),
          const SizedBox(width: 8),
          Text(
            text,
            style: TextStyle(
              fontSize: 14,
              color: textColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMachineDetailsContent(
      BuildContext context, NewItemModel item, Map<String, dynamic> stats) {
    final colorScheme = Theme.of(context).colorScheme;

    // Get needles required from stats
    int needlesRequired = stats['maxWidth'] ?? 0;

    // Check if there are enough needles
    final needlesAvailable = item.knittingMachine?.needlesCount ?? 0;
    final isEnough = needlesAvailable >= needlesRequired && needlesRequired > 0;

    // Determine the appropriate colors based on theme and status
    final successBgColor = isEnough
        ? colorScheme.primaryContainer.withOpacity(0.3)
        : colorScheme.errorContainer.withOpacity(0.3);

    final successTextColor = isEnough ? colorScheme.primary : colorScheme.error;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (item.knittingMachine?.customName != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Text(
              item.knittingMachine!.customName,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
            ),
          ),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: successBgColor,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'knittingInstructions_summary_machine_needlesRequired'.tr,
                      style: TextStyle(
                        fontSize: 14,
                        color: successTextColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '$needlesRequired',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: successTextColor,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'knittingInstructions_summary_machine_needlesAvailable'
                          .tr,
                      style: TextStyle(
                        fontSize: 14,
                        color: successTextColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${item.knittingMachine?.needlesCount ?? 0}',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: successTextColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
